/* eslint-disable max-classes-per-file */
import assert from "node:assert";
import MiniSearch from "minisearch";

interface Idiosyncrasies {
  directors: Record<string, string | undefined>;
  titles: Record<string, string | undefined>;
}

export interface MovieToResolve {
  directors: string[];
  titles: string[];
  years: number[];
}

export interface InternalMovie {
  kinopoiskId: string;
  titles: string[];
  directors: string[];
  year?: number;
  topPosition?: number;
}

export interface ResolvedMovie {
  kinopoiskId: string;
  topPosition?: number;
}

interface SearchMovie {
  id: string;
  titles: string[];
  directors: string[];
  year?: number;
}

export interface MovieRepository {
  findAll(): Promise<InternalMovie[]>;
}

export default class OpenAIMovieSearch {
  constructor(private movieRepository: MovieRepository) {}

  async search(text: string): Promise<(ResolvedMovie | null)[]> {
    return this.resolveMovies([
      {
        directors: ["Квентин Тарантино"],
        titles: ["Джанго освобожденный"],
        years: [2012],
      },
    ]);
  }

  async resolveMovies(
    moviesToResolve: MovieToResolve[],
  ): Promise<(ResolvedMovie | null)[]> {
    const movieSearch = new MiniSearch<SearchMovie>({
      fields: ["titles", "directors"],
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any
      extractField: (document: any, fieldName) =>
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any
        Array.isArray(document[fieldName])
          ? // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any
            document[fieldName].join(" ")
          : // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any
            document[fieldName],
      processTerm: (term) =>
        term
          .replace(/[-,.:;?!*+’]/g, "")
          .toLowerCase()
          .replace("ё", "е"),
    });
    const movies = await this.movieRepository.findAll();
    const searchMovies = movies.map(
      (movie): SearchMovie => ({
        id: movie.kinopoiskId,
        directors: movie.directors,
        titles: movie.titles,
      }),
    );
    const moviesMap = new Map(
      movies.map((movie) => [movie.kinopoiskId, movie]),
    );
    const resolvedMoviesMap = new Map<string, ResolvedMovie>(
      movies.map((movie) => [
        movie.kinopoiskId,
        { kinopoiskId: movie.kinopoiskId, topPosition: movie.topPosition },
      ]),
    );
    const searchMoviesMap = new Map(
      searchMovies.map((movie) => [movie.id, movie]),
    );

    movieSearch.addAll(searchMovies);

    const idiosyncrasies = deduceIdiosyncrasies(
      movieSearch,
      moviesMap,
      moviesToResolve,
    );

    return moviesToResolve.map((movieToResolve): ResolvedMovie | null => {
      const movie =
        findMovieByTitleAndYearAndDirector(
          movieSearch,
          moviesMap,
          idiosyncrasies,
          movieToResolve,
        ) ??
        (movieToResolve.directors.length === 1
          ? findMovieByYearAndDirector(
              movieSearch,
              moviesMap,
              idiosyncrasies,
              movieToResolve,
            ) ??
            findMovieByTitleAndYearWithoutDirector(
              movieSearch,
              moviesMap,
              idiosyncrasies,
              movieToResolve,
            )
          : null);

      if (movie != null) {
        const searchMovie = searchMoviesMap.get(movie.kinopoiskId);

        assert(searchMovie, "Movie not found");

        movieSearch.remove(searchMovie);

        return resolvedMoviesMap.get(movie.kinopoiskId)!;
      }

      return null;
    });
  }
}

function deduceIdiosyncrasies(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  moviesToResolve: MovieToResolve[],
): Idiosyncrasies {
  const idiosyncrasies = {
    directors: {} as Record<string, string>,
    titles: {} as Record<string, string>,
  };
  const directors = new Map<string, MovieToResolve[]>();

  for (const movie of moviesToResolve) {
    if (movie.directors.length > 1) {
      continue;
    }

    for (const director of movie.directors) {
      if (directors.has(director)) {
        directors.set(director, [...directors.get(director)!, movie]);
      } else {
        directors.set(director, [movie]);
      }
    }
  }

  for (const [director, filmography] of directors) {
    if (director in idiosyncrasies.directors || filmography.length === 0) {
      continue;
    }

    const directorResults = findMoviesByDirector(
      movieSearch,
      moviesMap,
      { directors: {}, titles: {} },
      director,
    );

    if (directorResults.length > 0) {
      continue;
    }

    const deducedDirector =
      filmography.length >= 2
        ? deduceDirectorFromFilmography(movieSearch, moviesMap, filmography) ??
          deduceDirectorFromFuzzyFullName(
            movieSearch,
            moviesMap,
            filmography[0],
          )
        : deduceDirectorFromFuzzyFullName(
            movieSearch,
            moviesMap,
            filmography[0],
          );

    if (deducedDirector != null) {
      idiosyncrasies.directors[director] = deducedDirector;
    }
  }

  return idiosyncrasies;
}

function deduceDirectorFromFilmography(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  filmography: MovieToResolve[],
): string | null {
  assert(
    filmography.length >= 2,
    "Cannot deduce director from filmography having less than 2 movies",
  );

  const suggestions = new Set<string>();

  for (const movieToResolve of filmography) {
    assert(
      movieToResolve.directors.length === 1,
      "Cannot deduce director from movie having two or more directors",
    );

    const movie = findMovieByTitleAndYear(
      movieSearch,
      moviesMap,
      { directors: {}, titles: {} },
      movieToResolve,
    );

    if (movie != null && movie.directors.length === 1) {
      suggestions.add(movie.directors[0]);
    }
  }

  if (suggestions.size === 1) {
    const [resolvedDirector] = suggestions.keys();

    return resolvedDirector;
  }

  return null;
}

function deduceDirectorFromFuzzyFullName(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  movieToResolve: MovieToResolve,
): string | null {
  const movie = findMovieByTitleAndYearAndFuzzyDirector(
    movieSearch,
    moviesMap,
    { directors: {}, titles: {} },
    movieToResolve,
  );

  return movie != null ? movie.directors[0] : null;
}

function findMovieByTitleAndYearWithoutDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  idiosyncrasies: Idiosyncrasies,
  movieToResolve: MovieToResolve,
): InternalMovie | null {
  const movie = findMovieByTitleAndYear(
    movieSearch,
    moviesMap,
    idiosyncrasies,
    movieToResolve,
  );

  return movie?.directors.length === 0 && movie.directors.length === 0
    ? movie
    : null;
}

function findMovieByTitleAndYear(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  idiosyncrasies: Idiosyncrasies,
  movieToResolve: MovieToResolve,
): InternalMovie | null {
  let movieResults = movieSearch
    .search({
      queries: [
        {
          fields: ["titles"],
          queries: movieToResolve.titles
            .map((title) => idiosyncrasies.titles[title.toUpperCase()] ?? title)
            .map((title) => ({
              combineWith: "AND",
              queries: [title],
              prefix: (term) => term === "vol",
            })),
          combineWith: "OR",
        },
      ],
      combineWith: "AND",
    })
    .map((result) => moviesMap.get(result.id as string)!)
    .filter(
      (movie) =>
        movie.year &&
        movieToResolve.years.some((year) => Math.abs(movie.year! - year) <= 1),
    );

  if (movieResults.length > 1) {
    movieResults = movieResults.filter((movie) =>
      movie.titles.some((title) =>
        movieToResolve.titles.some(
          (anotherTitle) =>
            idiosyncrasies.titles[anotherTitle] === title ||
            title.toUpperCase() === anotherTitle.toUpperCase(),
        ),
      ),
    );
  }

  return movieResults.length === 1 ? movieResults[0] : null;
}

function findMovieByTitleAndYearAndFuzzyDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  idiosyncrasies: Idiosyncrasies,
  movieToResolve: MovieToResolve,
): InternalMovie | null {
  let movieResults = movieSearch
    .search({
      queries: [
        {
          fields: ["titles"],
          queries: movieToResolve.titles
            .map((title) => idiosyncrasies.titles[title.toUpperCase()] ?? title)
            .map((title) => ({
              combineWith: "AND",
              queries: [title],
              prefix: (term) => term === "vol",
            })),
          combineWith: "OR",
        },
        {
          fields: ["directors"],
          queries: movieToResolve.directors
            .map((director) => idiosyncrasies.directors[director] ?? director)
            .map((director) => ({
              combineWith: "AND",
              queries: [director],
              fuzzy: 0.33,
            })),
          combineWith: "OR",
        },
      ],
      combineWith: "AND",
    })
    .map((result) => moviesMap.get(result.id as string)!)
    .filter(
      (movie) =>
        movie.year &&
        movieToResolve.years.some((year) => Math.abs(movie.year! - year) <= 1),
    );

  if (movieResults.length > 1) {
    movieResults = movieResults.filter((movie) =>
      movie.titles.some((title) =>
        movieToResolve.titles.some(
          (anotherTitle) =>
            idiosyncrasies.titles[anotherTitle] === title ||
            title.toUpperCase() === anotherTitle.toUpperCase(),
        ),
      ),
    );
  }

  return movieResults.length === 1 ? movieResults[0] : null;
}

function findMovieByTitleAndYearAndDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  idiosyncrasies: Idiosyncrasies,
  movieToResolve: MovieToResolve,
): InternalMovie | null {
  let movieResults = movieSearch
    .search({
      queries: [
        {
          fields: ["titles"],
          queries: movieToResolve.titles
            .map((title) => idiosyncrasies.titles[title.toUpperCase()] ?? title)
            .map((title) => ({
              combineWith: "AND",
              queries: [title],
              prefix: (term) => term === "vol",
            })),
          combineWith: "OR",
        },
        {
          fields: ["directors"],
          queries: movieToResolve.directors
            .map((director) => idiosyncrasies.directors[director] ?? director)
            .map((director) => ({
              combineWith: "AND",
              queries: [director],
            })),
          combineWith: "OR",
        },
      ],
      combineWith: "AND",
    })
    .map((result) => moviesMap.get(result.id as string)!)
    .filter(
      (movie) =>
        movie.year &&
        movieToResolve.years.some((year) => Math.abs(movie.year! - year) <= 1),
    );

  if (movieResults.length > 1) {
    movieResults = movieResults.filter((movie) =>
      movie.titles.some((title) =>
        movieToResolve.titles.some(
          (anotherTitle) =>
            idiosyncrasies.titles[anotherTitle] === title ||
            title.toUpperCase() === anotherTitle.toUpperCase(),
        ),
      ),
    );
  }

  return movieResults.length === 1 ? movieResults[0] : null;
}

function findMovieByYearAndDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  idiosyncrasies: Idiosyncrasies,
  movieToResolve: MovieToResolve,
): InternalMovie | null {
  assert(
    movieToResolve.directors.length === 1,
    "Cannot find movie by director if there are multiple directors",
  );

  const directorMovies = findMoviesByDirector(
    movieSearch,
    moviesMap,
    idiosyncrasies,
    movieToResolve.directors[0],
  );
  let movies = directorMovies;

  movies = movies.filter(
    (movie) =>
      movie.year &&
      movieToResolve.years.includes(movie.year) &&
      movie.directors.length === 1,
  );

  if (movies.length === 1) {
    // eslint-disable-next-line no-console
    console.log(
      `Isn't it the same movie?\n- ${movieToResolve.titles.join(" / ")} (${
        movieToResolve.directors[0]
      }, ${movieToResolve.years.join("/")})\n- ${movies[0].titles.join(
        " / ",
      )} (${movies[0].directors[0]}, ${movies[0].year})`,
    );
  }

  return null;
}

function findMoviesByDirector(
  movieSearch: MiniSearch<SearchMovie>,
  moviesMap: Map<string, InternalMovie>,
  idiosyncrasies: Idiosyncrasies,
  director: string,
): InternalMovie[] {
  return movieSearch
    .search({
      combineWith: "AND",
      fields: ["directors"],
      queries: [idiosyncrasies.directors[director] ?? director],
    })
    .map((result) => moviesMap.get(result.id as string)!);
}
