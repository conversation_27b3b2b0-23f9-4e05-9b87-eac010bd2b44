/* eslint-disable no-underscore-dangle */
import * as graphql from "../../web/graphql/types.generated.js";
import PostgresGraphQlResolverContext from "../PostgresGraphQlResolverContext.js";
import sql from "../sql.js";
import { batchResolveNested } from "./_common.js";

const Query: graphql.QueryResolvers<PostgresGraphQlResolverContext> = {
  me: {
    resolve: batchResolveNested(async (_prev, _args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<{
          id: string;
          name: string;
          slug: string;
        }>(sql`
          SELECT
            graphql_user.id::text as "id",
            graphql_user.name as "name",
            graphql_user.slug as "slug"
          FROM
            graphql_user
          WHERE
            graphql_user.id = ${Number(context.accountId)}
        `);

        const user = rows[0];

        if (!user) {
          throw new Error(`User not found: ${context.accountId}`);
        }

        return {
          __typename: "Account",
          id: user.id,
          name: user.name,
          slug: user.slug,
          kinopoiskUrl: `https://www.kinopoisk.ru/user/${user.id}`,
          isDemoAccount: context.isDemoAccount,
        };
      });
    }),
  },

  feed: {
    resolve: batchResolveNested(async (_prev, _args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["FeedItem"]
        >(sql`
          SELECT
            'UserMark' as "__typename",
            json_build_object(
              'id', graphql_feed.movie_id::text
            ) as "movie",
            graphql_feed."date" as "date",
            graphql_feed.mark as "mark",
            json_build_object(
              'id', graphql_feed.friend_id::text
            ) as "user"
          FROM
            graphql_feed
          WHERE
            graphql_feed.user_id = ${Number(context.accountId)}
          ORDER BY
            graphql_feed."date" DESC,
            graphql_feed.movie_id DESC
          LIMIT
            (
              SELECT
                GREATEST(COUNT(*), 30)
              FROM
                graphql_feed
              WHERE
                graphql_feed.date >= (NOW() - '2 days'::interval)
                AND graphql_feed.user_id = ${Number(context.accountId)}
            )
        `);

        return rows;
      });
    }),
  },

  genres: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        await connection.query(
          sql`SELECT SETSEED(EXTRACT(DOY FROM NOW()) / 367.0);`,
        );

        const rows = await connection.query<
          graphql.ResolversTypes["Genre"]
        >(sql`
          SELECT
            graphql_genre.id::text as "id"
          FROM
            graphql_genre
          ORDER BY
            ${sql.raw(
              {
                [graphql.GenreSort.Default]: `graphql_genre."order"`,
                [graphql.GenreSort.Random]: `RANDOM()`,
              }[args.sort ?? graphql.GenreSort.Default],
            )}
          ${args.limit ? sql`LIMIT ${Math.min(args.limit, 250)}` : sql``}
        `);

        return rows;
      });
    }),
  },

  movie: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["Movie"]
        >(sql`
          SELECT
            graphql_movie.id::text as "id"
          FROM
            graphql_movie
          WHERE
            (${
              args.kinopoiskId
            }::text IS NOT NULL AND graphql_movie.id = ${Number(
              args.kinopoiskId,
            )}::int)
            OR (${args.slug}::text IS NOT NULL AND graphql_movie.slug = ${
              args.slug
            }::text)
        `);

        return rows[0] ?? null;
      });
    }),
  },

  person: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["Person"]
        >(sql`
          SELECT
            graphql_person.id::text as "id"
          FROM
            graphql_person
          WHERE
            graphql_person.id = ${
              Number.isNaN(Number(args.idOrSlug)) ? null : Number(args.idOrSlug)
            }
            OR graphql_person.slug = ${args.idOrSlug}
        `);

        return rows[0] ?? null;
      });
    }),
  },

  movieViews: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["MovieView"]
        >(sql`
          SELECT
            json_build_object(
              'id', graphql_user_movie_view.movie_id::text
            ) as "movie",
            graphql_user_movie_view.timestamp as "timestamp",
            graphql_user_movie_mark.mark as "mark"
          FROM
            graphql_user_movie_view
          LEFT JOIN
            graphql_user_movie_mark
            ON graphql_user_movie_mark.user_id = graphql_user_movie_view.user_id
            AND graphql_user_movie_mark.movie_id = graphql_user_movie_view.movie_id
          WHERE
            graphql_user_movie_view.user_id = (
              SELECT
                id
              FROM
                graphql_user
              WHERE
                graphql_user.slug = ${args.userSlug}
            )
            AND (
              ${typeof args.decade === "undefined"}
              OR EXISTS (
                SELECT
                FROM
                graphql_movie
                WHERE
                  graphql_movie.id = graphql_user_movie_view.movie_id
                  AND graphql_movie.year - (graphql_movie.year % 10) = ${Number(
                    args.decade ?? null,
                  )}
              )
            ) AND (
              ${typeof args.director === "undefined"}
              OR EXISTS (
                SELECT
                FROM
                  graphql_movie_director
                JOIN
                  graphql_person
                  ON graphql_person.id = graphql_movie_director.person_id
                WHERE
                  graphql_movie_director.movie_id = graphql_user_movie_view.movie_id
                  AND graphql_person.slug = ${args.director ?? null}
              )
            ) AND (
              ${typeof args.genre === "undefined"}
              OR EXISTS (
                SELECT
                FROM
                  graphql_movie_genre
                JOIN
                  graphql_genre
                  ON graphql_genre.id = graphql_movie_genre.genre_id
                WHERE
                  graphql_movie_genre.movie_id = graphql_user_movie_view.movie_id
                  AND graphql_genre.slug = ${args.genre ?? null}
              )
            )
          ORDER BY
            graphql_user_movie_view.timestamp DESC,
            graphql_user_movie_view.movie_id DESC
        `);

        return rows;
      });
    }),
  },

  movieViewsByDecade: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["MovieViewDecadeGroup"]
        >(sql`
          WITH
            marks as (
              SELECT
                graphql_user_movie_mark.movie_id as movie_id,
                graphql_user_movie_mark.mark as mark
              FROM
                graphql_user
              JOIN
                graphql_user_movie_mark
                ON graphql_user_movie_mark.user_id = graphql_user.id
              JOIN
                graphql_movie
                ON graphql_movie.id = graphql_user_movie_mark.movie_id
              WHERE
                graphql_user.slug = ${args.userSlug}
              ORDER BY
                graphql_user_movie_mark.mark DESC,
                graphql_movie.year DESC
            )

            SELECT
              json_build_object(
                'id', (graphql_movie.year - graphql_movie.year % 10)::text,
                'label', graphql_movie.year - graphql_movie.year % 10 || '-е'
              ) as "decade",
              COUNT(*) as "totalCount",
              COUNT(*) FILTER (WHERE marks.mark >= 9 AND marks.mark <= 10) as "bestMarksCount",
              COUNT(*) FILTER (WHERE marks.mark >= 7 AND marks.mark <= 8) as "goodMarksCount",
              COUNT(*) FILTER (WHERE marks.mark >= 1 AND marks.mark <= 6) as "badMarksCount",
              COALESCE(
                ARRAY_AGG(
                  json_build_object(
                    'mark', marks.mark,
                    'movie', json_build_object(
                      'id', marks.movie_id::text
                    )
                  )
                  ORDER BY
                    marks.mark DESC,
                    marks.movie_id DESC
                ) FILTER (WHERE marks.mark >= ${
                  args.mark__gte ?? 0
                } AND marks.mark <= ${args.mark__lte ?? 10}),
                ARRAY[]::json[]
              ) as "views"
            FROM
              marks
            JOIN
              graphql_movie
              ON graphql_movie.id = marks.movie_id
            GROUP BY
              graphql_movie.year - graphql_movie.year % 10
            HAVING
              COUNT(*) FILTER (WHERE marks.mark >= ${
                args.mark__gte ?? 0
              } AND marks.mark <= ${args.mark__lte ?? 10}) > 0
            ORDER BY
              graphql_movie.year - graphql_movie.year % 10 DESC
        `);

        return rows;
      });
    }),
  },

  movieViewsByDirector: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["MovieViewDirectorGroup"]
        >(sql`
          WITH
            marks as (
              SELECT
                graphql_user_movie_mark.movie_id as movie_id,
                graphql_user_movie_mark.mark as mark
              FROM
                graphql_user
              JOIN
                graphql_user_movie_mark
                ON graphql_user_movie_mark.user_id = graphql_user.id
              JOIN
                graphql_movie
                ON graphql_movie.id = graphql_user_movie_mark.movie_id
              WHERE
                graphql_user.slug = ${args.userSlug}
              ORDER BY
                graphql_user_movie_mark.mark DESC,
                graphql_movie.year DESC
            )
          
            SELECT
              json_build_object(
                'id', graphql_movie_director.person_id::text
              ) as "director",
              COUNT(*) as "totalCount",
              COUNT(*) FILTER (WHERE marks.mark >= 9 AND marks.mark <= 10) as "bestMarksCount",
              COUNT(*) FILTER (WHERE marks.mark >= 7 AND marks.mark <= 8) as "goodMarksCount",
              COUNT(*) FILTER (WHERE marks.mark >= 1 AND marks.mark <= 6) as "badMarksCount",
              COALESCE(
                ARRAY_AGG(
                  json_build_object(
                    'mark', marks.mark,
                    'movie', json_build_object(
                      'id', marks.movie_id::text
                    )
                  )
                  ORDER BY
                    marks.mark DESC,
                    marks.movie_id DESC
                ) FILTER (WHERE marks.mark >= ${
                  args.mark__gte ?? 0
                } AND marks.mark <= ${args.mark__lte ?? 10}),
                ARRAY[]::json[]
              ) as "views"
            FROM
              marks
            JOIN
              graphql_movie_director
              ON graphql_movie_director.movie_id = marks.movie_id
            GROUP BY
              graphql_movie_director.person_id
            HAVING
              COUNT(*) FILTER (WHERE marks.mark >= ${
                args.mark__gte ?? 7
              } AND marks.mark <= ${args.mark__lte ?? 10}) > 1
            ORDER BY
              ${sql.raw(
                {
                  [graphql.AggregatedMoviesSort
                    .TotalCount]: `"totalCount" DESC, graphql_movie_director.person_id DESC`,
                  [graphql.AggregatedMoviesSort
                    .MarksOlymplicCount]: `"bestMarksCount" DESC, "goodMarksCount" DESC, graphql_movie_director.person_id DESC`,
                }[args.sort],
              )}
        `);

        return rows;
      });
    }),
  },

  movieViewsByGenre: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["MovieViewGenreGroup"]
        >(sql`
          WITH
            marks as (
              SELECT
                graphql_user_movie_mark.movie_id as movie_id,
                graphql_user_movie_mark.mark as mark
              FROM
                graphql_user
              JOIN
                graphql_user_movie_mark
                ON graphql_user_movie_mark.user_id = graphql_user.id
              JOIN
                graphql_movie
                ON graphql_movie.id = graphql_user_movie_mark.movie_id
              WHERE
                graphql_user.slug = ${args.userSlug}
              ORDER BY
                graphql_user_movie_mark.mark DESC,
                graphql_movie.year DESC
            )

            SELECT
              json_build_object(
                'id', graphql_movie_genre.genre_id::text
              ) as "genre",
              COUNT(*) as "totalCount",
              COUNT(*) FILTER (WHERE marks.mark >= 9 AND marks.mark <= 10) as "bestMarksCount",
              COUNT(*) FILTER (WHERE marks.mark >= 7 AND marks.mark <= 8) as "goodMarksCount",
              COUNT(*) FILTER (WHERE marks.mark >= 1 AND marks.mark <= 6) as "badMarksCount",
              COALESCE(
                ARRAY_AGG(
                  json_build_object(
                    'mark', marks.mark,
                    'movie', json_build_object(
                      'id', marks.movie_id::text
                    )
                  )
                  ORDER BY
                    marks.mark DESC,
                    marks.movie_id DESC
                ) FILTER (WHERE marks.mark >= ${
                  args.mark__gte ?? 0
                } AND marks.mark <= ${args.mark__lte ?? 10}),
                ARRAY[]::json[]
              ) as "views"
            FROM
              marks
            JOIN
              graphql_movie_genre
              ON graphql_movie_genre.movie_id = marks.movie_id
            GROUP BY
              graphql_movie_genre.genre_id
            ORDER BY
              ${sql.raw(
                {
                  [graphql.AggregatedMoviesSort
                    .TotalCount]: `"totalCount" DESC`,
                  [graphql.AggregatedMoviesSort
                    .MarksOlymplicCount]: `"bestMarksCount" DESC, "goodMarksCount" DESC`,
                }[args.sort],
              )}
        `);

        return rows;
      });
    }),
  },

  movieViewStats: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["MovieViewStats"]
        >(sql`
          WITH
            marks as (
              SELECT
                graphql_user_movie_mark.mark as mark
              FROM
                graphql_user
              JOIN
                graphql_user_movie_mark
                ON graphql_user_movie_mark.user_id = graphql_user.id
              WHERE
                graphql_user.slug = ${args.userSlug}
            )
          
          SELECT
            (
              SELECT
                COUNT(*)
              FROM
                marks
            ) as "totalCount",
            (
              SELECT
                ARRAY[
                  COUNT(*) FILTER (WHERE marks.mark >= 9 AND marks.mark <= 10),
                  COUNT(*) FILTER (WHERE marks.mark >= 7 AND marks.mark <= 8),
                  COUNT(*) FILTER (WHERE marks.mark >= 5 AND marks.mark <= 6),
                  COUNT(*) FILTER (WHERE marks.mark >= 1 AND marks.mark <= 4)
                ]
              FROM
                marks
            ) as "marksCount";     
        `);

        return rows[0] ?? null;
      });
    }),
  },

  movieViewStatsByDecade: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["MovieViewByDecadeStats"]
        >(sql`
          WITH
            marks as (
              SELECT
                graphql_user_movie_mark.movie_id as movie_id,
                graphql_user_movie_mark.mark as mark
              FROM
                graphql_user
              JOIN
                graphql_user_movie_mark
                ON graphql_user_movie_mark.user_id = graphql_user.id
              WHERE
                graphql_user.slug = ${args.userSlug}
            )

            SELECT
              json_build_object(
                'id', (graphql_movie.year - graphql_movie.year % 10)::text,
                'label', graphql_movie.year - graphql_movie.year % 10 || '-е'
              ) as "decade",
              COUNT(*) as "totalCount",
              ARRAY[
                COUNT(*) FILTER (WHERE marks.mark >= 9 AND marks.mark <= 10),
                COUNT(*) FILTER (WHERE marks.mark >= 7 AND marks.mark <= 8),
                COUNT(*) FILTER (WHERE marks.mark >= 5 AND marks.mark <= 6),
                COUNT(*) FILTER (WHERE marks.mark >= 1 AND marks.mark <= 4)
              ] as "marksCount"
            FROM
              marks
            JOIN
              graphql_movie
              ON graphql_movie.id = marks.movie_id
            GROUP BY
              graphql_movie.year - graphql_movie.year % 10
            ORDER BY
              graphql_movie.year - graphql_movie.year % 10 DESC
        `);

        return rows;
      });
    }),
  },

  movieViewStatsByDirector: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["MovieViewByDirectorStats"]
        >(sql`
          WITH
            marks as (
              SELECT
                graphql_user_movie_mark.movie_id as movie_id,
                graphql_user_movie_mark.mark as mark
              FROM
                graphql_user
              JOIN
                graphql_user_movie_mark
                ON graphql_user_movie_mark.user_id = graphql_user.id
              WHERE
                graphql_user.slug = ${args.userSlug}
            )
          
            SELECT
              json_build_object(
                'id', graphql_movie_director.person_id::text
              ) as "person",
              COUNT(*) as "totalCount",
              ARRAY[
                COUNT(*) FILTER (WHERE marks.mark >= 9 AND marks.mark <= 10),
                COUNT(*) FILTER (WHERE marks.mark >= 7 AND marks.mark <= 8),
                COUNT(*) FILTER (WHERE marks.mark >= 5 AND marks.mark <= 6),
                COUNT(*) FILTER (WHERE marks.mark >= 1 AND marks.mark <= 4)
              ] as "marksCount"
            FROM
              marks
            JOIN
              graphql_movie_director
              ON graphql_movie_director.movie_id = marks.movie_id
            GROUP BY
              graphql_movie_director.person_id
            ORDER BY
              ${sql.raw(
                {
                  [graphql.AggregatedMoviesSort
                    .TotalCount]: `"totalCount" DESC, graphql_movie_director.person_id DESC`,
                  [graphql.AggregatedMoviesSort
                    .MarksOlymplicCount]: `"marksCount" DESC, graphql_movie_director.person_id DESC`,
                }[args.sort],
              )}
            LIMIT
              10
        `);

        return rows;
      });
    }),
  },

  movieViewStatsByGenre: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["MovieViewStatsByGenre"]
        >(sql`
          WITH
            marks as (
              SELECT
                graphql_user_movie_mark.movie_id as movie_id,
                graphql_user_movie_mark.mark as mark
              FROM
                graphql_user
              JOIN
                graphql_user_movie_mark
                ON graphql_user_movie_mark.user_id = graphql_user.id
              WHERE
                graphql_user.slug = ${args.userSlug}
            )

            SELECT
              json_build_object(
                'id', graphql_movie_genre.genre_id::text
              ) as "genre",
              COUNT(*) as "totalCount",
              ARRAY[
                COUNT(*) FILTER (WHERE marks.mark >= 9 AND marks.mark <= 10),
                COUNT(*) FILTER (WHERE marks.mark >= 7 AND marks.mark <= 8),
                COUNT(*) FILTER (WHERE marks.mark >= 5 AND marks.mark <= 6),
                COUNT(*) FILTER (WHERE marks.mark >= 1 AND marks.mark <= 4)
              ] as "marksCount"
            FROM
              marks
            JOIN
              graphql_movie_genre
              ON graphql_movie_genre.movie_id = marks.movie_id
            GROUP BY
              graphql_movie_genre.genre_id
            ORDER BY
              ${sql.raw(
                {
                  [graphql.AggregatedMoviesSort
                    .TotalCount]: `"totalCount" DESC`,
                  [graphql.AggregatedMoviesSort
                    .MarksOlymplicCount]: `"marksCount" DESC`,
                }[args.sort],
              )}
        `);

        return rows;
      });
    }),
  },

  movies: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        await connection.query(
          sql`SELECT SETSEED(EXTRACT(DOY FROM NOW()) / 367.0);`,
        );

        const movies = await connection.query<
          graphql.ResolversTypes["Movie"]
        >(sql`
            SELECT
              graphql_movie.id::text as "id"
            FROM
              graphql_movie
            LEFT JOIN
              graphql_movie_top
              ON graphql_movie_top.movie_id = graphql_movie.id
              AND graphql_movie_top.user_id = ${Number(context.accountId)}
            WHERE
              (
                ${args.sort !== graphql.MovieSort.Random}
                OR graphql_movie_top.good_marks_percentage >= 70
                OR graphql_movie_top.best_marks_percentage >= 5
              )
              AND (${args.viewed !== true} OR EXISTS (
                SELECT
                FROM
                  graphql_user_movie_view
                WHERE
                  graphql_user_movie_view.movie_id = graphql_movie.id
                  AND graphql_user_movie_view.user_id = ${Number(
                    context.accountId,
                  )}
                )
              )
              AND (${args.viewed !== false} OR NOT EXISTS (
                SELECT
                FROM
                  graphql_user_movie_view
                WHERE
                  graphql_user_movie_view.movie_id = graphql_movie.id
                  AND graphql_user_movie_view.user_id = ${Number(
                    context.accountId,
                  )}
                )
              )
              AND (${args.genreSlug == null} OR EXISTS (
                SELECT
                FROM
                  graphql_movie_genre
                WHERE
                  graphql_movie_genre.movie_id = graphql_movie.id
                  AND graphql_movie_genre.genre_id = (
                    SELECT
                      graphql_genre.id
                    FROM
                      graphql_genre
                    WHERE
                      graphql_genre.slug = ${args.genreSlug ?? null}
                  )
                )
              )
            ORDER BY
              ${sql.raw(
                {
                  [graphql.MovieSort
                    .Chronological]: `graphql_movie.year ASC, graphql_movie.id DESC`,
                  [graphql.MovieSort
                    .ReverseChronological]: `graphql_movie.year DESC, graphql_movie.id DESC`,
                  [graphql.MovieSort
                    .BestFirst]: `graphql_movie_top."position" ASC, graphql_movie.id DESC`,
                  [graphql.MovieSort.Random]: `RANDOM()`,
                }[args.sort],
              )}
            OFFSET
              ${args.offset ?? 0}
            LIMIT
              ${Math.min(args.limit ?? 10, 250)}
        `);

        return movies;
      });
    }),
  },

  search: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      if (args.text.trimStart().length === 0) {
        return [];
      }

      return context.pool.transaction(async (connection) => {
        const query = args.text
          .trimStart()
          .replace(/Ё/g, "Е")
          .replace(/ё/g, "е");
        const words = query.split(" ").filter(Boolean);
        const partialQuery = words.map((word) => `'${word}':*`).join(" & ");
        const searchResults = await connection.query<
          | (graphql.ResolversTypes["MovieSearchResult"] & {
              __typename: "MovieSearchResult";
            })
          | (graphql.ResolversTypes["PersonSearchResult"] & {
              __typename: "PersonSearchResult";
            })
        >(sql`
        SELECT
          *
        FROM
          (
            SELECT
              'MovieSearchResult' as "__typename",
              json_build_object(
                'id', graphql_movie.id::text
              ) as "movie",
              NULL as "person",
              ts_rank_cd(graphql_movie.ts, plainto_tsquery('simple', ${query}), 8 | 32)
                + 0.01 * ts_rank_cd(graphql_movie.ts, to_tsquery('simple', ${partialQuery}), 8 | 32) as "relevance"
            FROM
              graphql_movie
            WHERE
              graphql_movie.ts_text ILIKE ${`%${query}%`}

            UNION ALL

            SELECT
              'PersonSearchResult' as "__typename",
              NULL as "movie",
              json_build_object(
                'id', graphql_person.id::text
              ) as "person",
              ts_rank_cd(graphql_person.ts, plainto_tsquery('simple', ${query}), 8 | 32)
                + 0.01 * ts_rank_cd(graphql_person.ts, to_tsquery('simple', ${partialQuery}), 8 | 32) as "relevance"
            FROM
              graphql_person
            WHERE
              graphql_person.ts_text ILIKE ${`%${query}%`}
          ) results
        ORDER BY
          relevance DESC
        LIMIT
          20
      `);

        let searchResultGroups: graphql.SearchResultGroup[] = [
          {
            __typename: "SearchResultGroup",
            label: "Фильмы",
            results: searchResults.filter(
              (result): result is graphql.MovieSearchResult =>
                result.__typename === "MovieSearchResult",
            ),
          },
          {
            __typename: "SearchResultGroup",
            label: "Персоны",
            results: searchResults.filter(
              (result): result is graphql.PersonSearchResult =>
                result.__typename === "PersonSearchResult",
            ),
          },
        ];

        searchResultGroups = searchResultGroups.filter(
          (group) => group.results.length > 0,
        );

        return searchResultGroups;
      });
    }),
  },

  user: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<graphql.ResolversTypes["User"]>(sql`
          SELECT
            graphql_user.id::text as "id",
            graphql_user.name as "name"
          FROM
            graphql_user
          WHERE
            graphql_user.slug = ${args.userSlug}
        `);

        return rows[0] ?? null;
      });
    }),
  },

  watchlist: {
    resolve: batchResolveNested(async (_prev, args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversTypes["WatchlistItem"]
        >(sql`
          SELECT
            json_build_object(
              'id', graphql_watchlist.movie_id::text
            ) as "movie",
            graphql_watchlist."timestamp" as "timestamp",
            graphql_user_movie_mark.mark as "mark"
          FROM
            graphql_user
          JOIN
            graphql_watchlist
            ON graphql_watchlist.user_id = graphql_user.id
          LEFT JOIN
            graphql_user_movie_mark
            ON graphql_user_movie_mark.user_id = graphql_user.id
            AND graphql_user_movie_mark.movie_id = graphql_watchlist.movie_id
          WHERE
            graphql_user.slug = ${args.userSlug}
          ORDER BY
            timestamp DESC
        `);

        return rows;
      });
    }),
  },
};

export default Query;
