import { InternalMovie, MovieRepository } from "../openai/OpenAIMovieSearch.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresKinanetMovieRepository implements MovieRepository {
  constructor(private pool: ConnectionPool) {}

  async findAll(): Promise<InternalMovie[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        titles: (string | null)[];
        directors: (string | null)[] | null;
        top_position: number | null;
        year: number;
      }>(sql`
        SELECT DISTINCT ON (kinopoisk_movie.id)
          kinopoisk_movie.id as id,
          ARRAY[
            kinopoisk_movie.title,
            wikidata_movie.title_en,
            wikidata_movie.title_ru
          ] as titles,
          directors.full_names as directors,
          graphql_movie_top."position" as top_position,
          kinopoisk_movie.year as year
        FROM
          kinopoisk_movie
        LEFT JOIN
          wikidata_movie
          ON wikidata_movie.kinopoisk_id = kinopoisk_movie.id
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(wikidata_person.full_name_ru) as full_names
            FROM
              wikidata_movie_crew_member
            JOIN
              wikidata_person
              ON wikidata_person.id = wikidata_movie_crew_member.person_id
            WHERE
              wikidata_movie_crew_member.movie_id = wikidata_movie.id
              AND wikidata_movie_crew_member.role = 'director'
          ) "directors"
          ON TRUE
        LEFT JOIN
          graphql_movie_top
          ON graphql_movie_top.movie_id = kinopoisk_movie.id
          AND graphql_movie_top.user_id = 789114
        ORDER BY
          kinopoisk_movie.id ASC;
      `);

      return rows.map(
        (row): InternalMovie => ({
          kinopoiskId: String(row.id),
          directors: (row.directors ?? []).filter(
            (x): x is string => x !== null,
          ),
          titles: row.titles.filter((x): x is string => x !== null),
          topPosition: row.top_position ?? undefined,
          year: row.year ?? null,
        }),
      );
    });
  }
}
