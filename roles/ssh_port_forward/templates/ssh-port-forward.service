[Unit]
Description=SSH Tunnel
After=network.target

[Service]
Restart=on-failure
RestartSec=20
User=nikityy
ExecStart=/bin/ssh -NT -o ServerAliveInterval=60 -o ExitOnForwardFailure=yes {% for port_forward in ssh_tunnel_local_port_forwards %} -L {{ port_forward.local_host_port }}:localhost:{{ port_forward.remote_host_port }}{% endfor %} {% for port_forward in ssh_tunnel_remote_port_forwards %} -R {{ port_forward.remote_host_port }}:localhost:{{ port_forward.local_host_port }}{% endfor %} {{ ssh_tunnel_server_host }}

[Install]
WantedBy=multi-user.target
